import { Injectable, NotFoundException } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"
import type { CreateHistoryDto } from "./dto/create-history.dto"
import type { Query, CollectionReference } from "firebase-admin/firestore"

export interface HistoryRecord {
  id: string
  taskId?: string
  userId?: string
  robotId?: string
  zoneId?: string
  action?: string
  timestamp?: string | Date
  duration?: string
  result?: string
  errorMessage?: string
  batteryUsed?: string
  disinfectionEfficiency?: string
  // ...add other fields as needed
}

@Injectable()
export class HistoryService {
  private readonly collection = "history"

  constructor(private firebaseService: FirebaseService) {}

  async create(createHistoryDto: CreateHistoryDto) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc()

    const historyData = {
      ...createHistoryDto,
      historyId: docRef.id,
      timestamp: new Date(),
    }

    await docRef.set(historyData)
    return historyData
  }

  async findAll() {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).orderBy("timestamp", "desc").get()
    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findByTask(taskId: string) {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore
      .collection(this.collection)
      .where("taskId", "==", taskId)
      .orderBy("timestamp", "desc")
      .get()

    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findByUser(userId: string) {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore
      .collection(this.collection)
      .where("userId", "==", userId)
      .orderBy("timestamp", "desc")
      .get()

    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findByRobot(robotId: string) {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore
      .collection(this.collection)
      .where("robotId", "==", robotId)
      .orderBy("timestamp", "desc")
      .get()

    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findById(id: string) {
    const firestore = this.firebaseService.getFirestore()
    const doc = await firestore.collection(this.collection).doc(id).get()

    if (!doc.exists) {
      throw new NotFoundException(`History record with ID ${id} not found`)
    }

    return { id: doc.id, ...doc.data() }
  }

  async generateReport(filters: any) {
    const firestore = this.firebaseService.getFirestore()
    let query: Query = firestore.collection(this.collection)

    if (filters.startDate && filters.endDate) {
      query = query
        .where("timestamp", ">=", new Date(filters.startDate))
        .where("timestamp", "<=", new Date(filters.endDate))
    }

    if (filters.userId) {
      query = query.where("userId", "==", filters.userId)
    }

    if (filters.robotId) {
      query = query.where("robotId", "==", filters.robotId)
    }

    if (filters.action) {
      query = query.where("action", "==", filters.action)
    }

    const snapshot = await query.orderBy("timestamp", "desc").get()
    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() } as HistoryRecord))
  }

  async exportData(filters: any) {
    const data = await this.generateReport(filters)

    // Convert to CSV format
    const headers = [
      "ID",
      "Task ID",
      "User ID",
      "Robot ID",
      "Zone ID",
      "Action",
      "Timestamp",
      "Duration",
      "Result",
      "Error Message",
      "Battery Used",
      "Disinfection Efficiency",
    ]

    const csvData = [
      headers.join(","),
      ...data.map((record: HistoryRecord) =>
        [
          record.id,
          record.taskId || "",
          record.userId || "",
          record.robotId || "",
          record.zoneId || "",
          record.action || "",
          record.timestamp || "",
          record.duration || "",
          record.result || "",
          record.errorMessage || "",
          record.batteryUsed || "",
          record.disinfectionEfficiency || "",
        ].join(","),
      ),
    ].join("\n")

    return {
      data: csvData,
      filename: `history_export_${new Date().toISOString().split("T")[0]}.csv`,
      contentType: "text/csv",
    }
  }

  async remove(id: string) {
    const firestore = this.firebaseService.getFirestore()
    await firestore.collection(this.collection).doc(id).delete()
    return { message: "History record deleted successfully" }
  }
}
