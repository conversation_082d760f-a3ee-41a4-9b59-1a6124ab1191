import { NestFactory } from "@nestjs/core"
import { Val<PERSON><PERSON>Pipe, Logger } from "@nestjs/common"
import { SwaggerModule, DocumentBuilder } from "@nestjs/swagger"
import { AppModule } from "./app.module"
import { AllExceptionsFilter } from "./common/filters/http-exception.filter"
import { LoggingInterceptor } from "./common/interceptors/logging.interceptor"
import { CustomValidationPipe } from "./common/pipes/validation.pipe"

async function bootstrap() {
  const logger = new Logger('Bootstrap')

  try {
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    })

    // Global exception filter
    app.useGlobalFilters(new AllExceptionsFilter())

    // Global logging interceptor
    app.useGlobalInterceptors(new LoggingInterceptor())

    // Global validation pipe
    app.useGlobalPipes(
      new CustomValidationPipe(),
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        disableErrorMessages: false,
      }),
    )

    // Enable CORS
    app.enableCors({
      origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
      credentials: true,
    })

    // API prefix
    app.setGlobalPrefix("api/v1")

    // Swagger documentation
    const config = new DocumentBuilder()
      .setTitle("Robot Disinfection Management API")
      .setDescription("Backend API for robot disinfection management system")
      .setVersion("1.0")
      .addBearerAuth()
      .addTag('Health', 'Health check endpoints')
      .addTag('Auth', 'Authentication endpoints')
      .addTag('Users', 'User management endpoints')
      .addTag('Rooms', 'Room management endpoints')
      .addTag('Robots', 'Robot management endpoints')
      .addTag('Tasks', 'Task management endpoints')
      .addTag('History', 'History and reporting endpoints')
      .build()

    const document = SwaggerModule.createDocument(app, config)
    SwaggerModule.setup("api/docs", app, document)

    const port = process.env.PORT || 3001
    await app.listen(port)

    logger.log(`🚀 Application is running on: http://localhost:${port}`)
    logger.log(`📚 Swagger docs available at: http://localhost:${port}/api/docs`)
    logger.log(`🏥 Health check available at: http://localhost:${port}/api/v1/health`)
  } catch (error) {
    logger.error('❌ Error starting the application', error)
    process.exit(1)
  }
}

bootstrap()
