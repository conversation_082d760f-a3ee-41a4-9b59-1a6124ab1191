"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const auth_module_1 = require("./modules/auth/auth.module");
const users_module_1 = require("./modules/users/users.module");
const tasks_module_1 = require("./modules/tasks/tasks.module");
const robots_module_1 = require("./modules/robots/robots.module");
const buildings_module_1 = require("./modules/buildings/buildings.module");
const floors_module_1 = require("./modules/floors/floors.module");
const rooms_module_1 = require("./modules/rooms/rooms.module");
const zones_module_1 = require("./modules/zones/zones.module");
const history_module_1 = require("./modules/history/history.module");
const firebase_module_1 = require("./config/firebase/firebase.module");
const health_controller_1 = require("./common/controllers/health.controller");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: ".env",
                validationOptions: {
                    allowUnknown: true,
                    abortEarly: false,
                },
            }),
            firebase_module_1.FirebaseModule,
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            tasks_module_1.TasksModule,
            robots_module_1.RobotsModule,
            buildings_module_1.BuildingsModule,
            floors_module_1.FloorsModule,
            rooms_module_1.RoomsModule,
            zones_module_1.ZonesModule,
            history_module_1.HistoryModule,
        ],
        controllers: [health_controller_1.HealthController],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map