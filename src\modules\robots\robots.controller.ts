import { Controller, Get, Post, Patch, Param, Delete, UseGuards } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiBearerAuth } from "@nestjs/swagger"
import { RobotsService } from "./robots.service"
import type { CreateRobotDto } from "./dto/create-robot.dto"
import type { UpdateRobotDto } from "./dto/update-robot.dto"
import { FirebaseAuthGuard } from "../../common/guards/firebase-auth.guard"
import { RolesGuard } from "../../common/guards/roles.guard"
import { Roles, UserRole } from "../../common/decorators/roles.decorator"

@ApiTags("Robots")
@Controller("robots")
@UseGuards(FirebaseAuthGuard, RolesGuard)
@ApiBearerAuth()
export class RobotsController {
  constructor(private readonly robotsService: RobotsService) {}

  @Post()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Register a new robot (Admin only)" })
  create(createRobotDto: CreateRobotDto) {
    return this.robotsService.create(createRobotDto)
  }

  @Get()
  @ApiOperation({ summary: "Get all robots" })
  findAll() {
    return this.robotsService.findAll()
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get robot by ID' })
  findOne(@Param('id') id: string) {
    return this.robotsService.findById(id);
  }

  @Patch(":id")
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Update robot (Admin only)" })
  update(@Param('id') id: string, updateRobotDto: UpdateRobotDto) {
    return this.robotsService.update(id, updateRobotDto)
  }

  @Patch(":id/status")
  @ApiOperation({ summary: "Update robot status" })
  updateStatus(@Param('id') id: string, status: any) {
    return this.robotsService.updateStatus(id, status)
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete robot (Admin only)' })
  remove(@Param('id') id: string) {
    return this.robotsService.remove(id);
  }
}
