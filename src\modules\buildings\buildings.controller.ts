import { Controller, Get, Post, Patch, Param, Delete, UseGuards, Body } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiBearerAuth } from "@nestjs/swagger"
import { BuildingsService } from "./buildings.service"
import type { CreateBuildingDto } from "./dto/create-building.dto"
import type { UpdateBuildingDto } from "./dto/update-building.dto"
import { FirebaseAuthGuard } from "../../common/guards/firebase-auth.guard"
import { RolesGuard } from "../../common/guards/roles.guard"
import { Roles, UserRole } from "../../common/decorators/roles.decorator"

@ApiTags("Buildings")
@Controller("buildings")
@UseGuards(FirebaseAuthGuard, RolesGuard)
@ApiBearerAuth()
export class BuildingsController {
  constructor(private readonly buildingsService: BuildingsService) {}

  @Post()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Create a new building (Admin only)" })
  create(@Body() createBuildingDto: CreateBuildingDto) {
    return this.buildingsService.create(createBuildingDto)
  }

  @Get()
  @ApiOperation({ summary: "Get all buildings" })
  findAll() {
    return this.buildingsService.findAll()
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get building by ID' })
  findOne(@Param('id') id: string) {
    return this.buildingsService.findById(id);
  }

  @Patch(":id")
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Update building (Admin only)" })
  update(@Param('id') id: string, @Body() updateBuildingDto: UpdateBuildingDto) {
    return this.buildingsService.update(id, updateBuildingDto)
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete building (Admin only)' })
  remove(@Param('id') id: string) {
    return this.buildingsService.remove(id);
  }
}
