import { FirebaseService } from "../../config/firebase/firebase.service";
import { UsersService } from "../users/users.service";
import type { RegisterDto } from "./dto/register.dto";
import type { LoginDto } from "./dto/login.dto";
export declare class AuthService {
    private firebaseService;
    private usersService;
    constructor(firebaseService: FirebaseService, usersService: UsersService);
    register(registerDto: RegisterDto): Promise<{
        note: string;
        message: string;
        userId: any;
    }>;
    validateUser(uid: string): Promise<any>;
    updateLastLogin(userId: string): Promise<void>;
    login(loginDto: LoginDto): Promise<{
        message: string;
        token: string;
        user: {
            uid: any;
            email: any;
            username: any;
            role: any;
        };
        note: string;
    }>;
}
