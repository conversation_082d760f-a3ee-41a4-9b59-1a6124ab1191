import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"
import { UserRole } from "../../../common/decorators/roles.decorator"

export class RegisterDto {
  @ApiProperty()
  @IsString()
  @MinLength(3)
  username: string

  @ApiProperty()
  @IsEmail()
  email: string

  @ApiProperty()
  @IsString()
  @MinLength(6)
  password: string

  @ApiProperty({ enum: UserRole, required: false })
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  language?: string
}
