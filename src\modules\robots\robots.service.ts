import { Injectable, NotFoundException } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"
import type { CreateRobotDto } from "./dto/create-robot.dto"
import type { UpdateRobotDto } from "./dto/update-robot.dto"

@Injectable()
export class RobotsService {
  private readonly collection = "robots"

  constructor(private firebaseService: FirebaseService) {}

  async create(createRobotDto: CreateRobotDto) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc()

    const robotData = {
      ...createRobotDto,
      robotId: docRef.id,
      isConnected: false,
      createdAt: new Date(),
    }

    await docRef.set(robotData)
    return robotData
  }

  async findAll() {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).get()
    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findById(id: string) {
    const firestore = this.firebaseService.getFirestore()
    const doc = await firestore.collection(this.collection).doc(id).get()

    if (!doc.exists) {
      throw new NotFoundException(`Robot with ID ${id} not found`)
    }

    return { id: doc.id, ...doc.data() }
  }

  async update(id: string, updateRobotDto: UpdateRobotDto) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc(id)

    await docRef.update({
      ...updateRobotDto,
      updatedAt: new Date(),
    })

    return this.findById(id)
  }

  async updateStatus(id: string, status: any) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc(id)

    await docRef.update({
      ...status,
      lastStatusUpdate: new Date(),
    })

    return this.findById(id)
  }

  async remove(id: string) {
    const firestore = this.firebaseService.getFirestore()
    await firestore.collection(this.collection).doc(id).delete()
    return { message: "Robot deleted successfully" }
  }
}
