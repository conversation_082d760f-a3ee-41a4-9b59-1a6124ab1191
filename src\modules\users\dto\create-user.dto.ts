import { <PERSON><PERSON><PERSON>, IsE<PERSON>, Is<PERSON>num, <PERSON><PERSON><PERSON>al, IsDate } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"
import { UserRole } from "../../../common/decorators/roles.decorator"

export class CreateUserDto {
  @ApiProperty()
  @IsString()
  userId: string

  @ApiProperty()
  @IsString()
  username: string

  @ApiProperty()
  @IsEmail()
  email: string

  @ApiProperty({ enum: UserRole })
  @IsEnum(UserRole)
  role: UserRole

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  language?: string

  @ApiProperty()
  @IsDate()
  createdAt: Date

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDate()
  lastLogin?: Date
}
