"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var FirebaseService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FirebaseService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const admin = __importStar(require("firebase-admin"));
const firestore_1 = require("firebase-admin/firestore");
const auth_1 = require("firebase-admin/auth");
let FirebaseService = FirebaseService_1 = class FirebaseService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(FirebaseService_1.name);
    }
    async onModuleInit() {
        try {
            await this.initializeFirebase();
            this.logger.log('Firebase initialized successfully');
        }
        catch (error) {
            this.logger.error('Failed to initialize Firebase', error);
            throw error;
        }
    }
    async initializeFirebase() {
        const requiredVars = [
            'FIREBASE_PROJECT_ID',
            'FIREBASE_PRIVATE_KEY_ID',
            'FIREBASE_PRIVATE_KEY',
            'FIREBASE_CLIENT_EMAIL',
            'FIREBASE_CLIENT_ID'
        ];
        for (const varName of requiredVars) {
            const value = this.configService.get(varName);
            if (!value) {
                throw new Error(`Missing required environment variable: ${varName}`);
            }
        }
        const serviceAccount = {
            type: "service_account",
            project_id: this.configService.get("FIREBASE_PROJECT_ID"),
            private_key_id: this.configService.get("FIREBASE_PRIVATE_KEY_ID"),
            private_key: this.configService.get("FIREBASE_PRIVATE_KEY")?.replace(/\\n/g, "\n"),
            client_email: this.configService.get("FIREBASE_CLIENT_EMAIL"),
            client_id: this.configService.get("FIREBASE_CLIENT_ID"),
            auth_uri: "https://accounts.google.com/o/oauth2/auth",
            token_uri: "https://oauth2.googleapis.com/token",
            auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
            client_x509_cert_url: this.configService.get("FIREBASE_CLIENT_CERT_URL"),
        };
        if (!admin.apps.length) {
            admin.initializeApp({
                credential: admin.credential.cert(serviceAccount),
                databaseURL: this.configService.get("FIREBASE_DATABASE_URL"),
            });
        }
        this.firestore = (0, firestore_1.getFirestore)();
        this.auth = (0, auth_1.getAuth)();
        await this.testConnection();
    }
    async testConnection() {
        try {
            await this.firestore.collection('_health_check').limit(1).get();
            this.logger.log('Firestore connection test successful');
        }
        catch (error) {
            this.logger.warn('Firestore connection test failed', error);
        }
    }
    getFirestore() {
        if (!this.firestore) {
            throw new Error('Firestore not initialized. Make sure Firebase service is properly configured.');
        }
        return this.firestore;
    }
    getAuth() {
        if (!this.auth) {
            throw new Error('Firebase Auth not initialized. Make sure Firebase service is properly configured.');
        }
        return this.auth;
    }
    async isHealthy() {
        try {
            if (!this.firestore || !this.auth) {
                return false;
            }
            await this.firestore.collection('_health_check').limit(1).get();
            return true;
        }
        catch (error) {
            this.logger.error('Firebase health check failed', error);
            return false;
        }
    }
};
exports.FirebaseService = FirebaseService;
exports.FirebaseService = FirebaseService = FirebaseService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], FirebaseService);
//# sourceMappingURL=firebase.service.js.map