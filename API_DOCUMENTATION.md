# Robot Disinfection Management API Documentation

## Overview

This is a comprehensive REST API for managing robot disinfection systems in healthcare facilities. The API provides endpoints for managing robots, tasks, buildings, floors, rooms, zones, users, and historical data.

**Base URL:** `http://localhost:3001/api/v1`  
**API Version:** 1.0  
**Authentication:** <PERSON><PERSON> (Firebase Auth)  
**Documentation:** Available at `http://localhost:3001/api/docs` (Swagger UI)

## Authentication

Most endpoints require authentication using Firebase Bearer tokens. Include the token in the Authorization header:

```
Authorization: Bearer <your-firebase-token>
```

### User Roles
- **USER**: Standard user with limited access
- **ADMIN**: Administrator with full access to all resources

## Health Check Endpoints

### GET /health
**Description:** General health check endpoint  
**Authentication:** None required  
**Response:**
```json
{
  "status": "ok",
  "timestamp": "2025-07-09T08:53:38.196Z",
  "uptime": 26.2289282,
  "environment": "development",
  "services": {
    "firebase": "healthy"
  }
}
```

### GET /health/firebase
**Description:** Firebase-specific health check  
**Authentication:** None required  

## Authentication Endpoints

### POST /auth/register
**Description:** Register a new user  
**Authentication:** None required  
**Request Body:**
```json
{
  "username": "string (min 3 chars)",
  "email": "<EMAIL>",
  "password": "string (min 6 chars)",
  "role": "user|admin (optional)",
  "language": "string (optional)"
}
```

### GET /auth/profile
**Description:** Get current user profile  
**Authentication:** Required  
**Response:** User profile information

## User Management Endpoints

### POST /users
**Description:** Create a new user (Admin only)  
**Authentication:** Required (Admin)  
**Request Body:** CreateUserDto

### GET /users
**Description:** Get all users (Admin only)  
**Authentication:** Required (Admin)  

### GET /users/:id
**Description:** Get user by ID  
**Authentication:** Required  
**Parameters:** `id` - User ID

### PATCH /users/:id
**Description:** Update user  
**Authentication:** Required  
**Parameters:** `id` - User ID  
**Request Body:** UpdateUserDto

### DELETE /users/:id
**Description:** Delete user (Admin only)  
**Authentication:** Required (Admin)  
**Parameters:** `id` - User ID

## Robot Management Endpoints

### POST /robots
**Description:** Register a new robot (Admin only)  
**Authentication:** Required (Admin)  
**Request Body:**
```json
{
  "robotName": "string",
  "serialNumber": "string",
  "batteryLevel": "number (optional)",
  "currentPosition": "string (optional)",
  "ipAddress": "valid IP (optional)",
  "firmwareVersion": "string (optional)",
  "maintenanceDate": "ISO date string (optional)",
  "currentTaskId": "string (optional)"
}
```

### GET /robots
**Description:** Get all robots  
**Authentication:** Required  

### GET /robots/:id
**Description:** Get robot by ID  
**Authentication:** Required  
**Parameters:** `id` - Robot ID

### PATCH /robots/:id
**Description:** Update robot (Admin only)  
**Authentication:** Required (Admin)  
**Parameters:** `id` - Robot ID  
**Request Body:** UpdateRobotDto

### PATCH /robots/:id/status
**Description:** Update robot status  
**Authentication:** Required  
**Parameters:** `id` - Robot ID  
**Request Body:**
```json
{
  "status": "idle|working|charging|maintenance|error (optional)",
  "batteryLevel": "number (optional)",
  "currentPosition": "string (optional)",
  "isConnected": "boolean (optional)",
  "currentTaskId": "string (optional)",
  "errorMessage": "string (optional)"
}
```

### DELETE /robots/:id
**Description:** Delete robot (Admin only)  
**Authentication:** Required (Admin)  
**Parameters:** `id` - Robot ID

## Task Management Endpoints

### POST /tasks
**Description:** Create a new task  
**Authentication:** Required  
**Request Body:** CreateTaskDto

### GET /tasks
**Description:** Get all tasks with optional filtering  
**Authentication:** Required  
**Query Parameters:**
- `userId` (optional) - Filter by user ID
- `robotId` (optional) - Filter by robot ID

### GET /tasks/:id
**Description:** Get task by ID  
**Authentication:** Required  
**Parameters:** `id` - Task ID

### PATCH /tasks/:id
**Description:** Update task  
**Authentication:** Required  
**Parameters:** `id` - Task ID  
**Request Body:** UpdateTaskDto

### PATCH /tasks/:id/status
**Description:** Update task status  
**Authentication:** Required  
**Parameters:** `id` - Task ID  
**Request Body:**
```json
{
  "status": "string"
}
```

### DELETE /tasks/:id
**Description:** Delete task  
**Authentication:** Required  
**Parameters:** `id` - Task ID

## Building Management Endpoints

### POST /buildings
**Description:** Create a new building (Admin only)  
**Authentication:** Required (Admin)  
**Request Body:** CreateBuildingDto

### GET /buildings
**Description:** Get all buildings  
**Authentication:** Required  

### GET /buildings/:id
**Description:** Get building by ID  
**Authentication:** Required  
**Parameters:** `id` - Building ID

### PATCH /buildings/:id
**Description:** Update building (Admin only)  
**Authentication:** Required (Admin)  
**Parameters:** `id` - Building ID  
**Request Body:** UpdateBuildingDto

### DELETE /buildings/:id
**Description:** Delete building (Admin only)  
**Authentication:** Required (Admin)  
**Parameters:** `id` - Building ID

## Floor Management Endpoints

### POST /floors
**Description:** Create a new floor (Admin only)  
**Authentication:** Required (Admin)  
**Request Body:** CreateFloorDto

### GET /floors
**Description:** Get all floors with optional filtering  
**Authentication:** Required  
**Query Parameters:**
- `buildingId` (optional) - Filter by building ID

### GET /floors/:id
**Description:** Get floor by ID  
**Authentication:** Required  
**Parameters:** `id` - Floor ID

### PATCH /floors/:id
**Description:** Update floor (Admin only)  
**Authentication:** Required (Admin)  
**Parameters:** `id` - Floor ID  
**Request Body:** UpdateFloorDto

### DELETE /floors/:id
**Description:** Delete floor (Admin only)  
**Authentication:** Required (Admin)  
**Parameters:** `id` - Floor ID

## Room Management Endpoints

### POST /rooms
**Description:** Create a new room (Admin only)  
**Authentication:** Required (Admin)  
**Request Body:** CreateRoomDto

### GET /rooms
**Description:** Get all rooms with optional filtering  
**Authentication:** Required  
**Query Parameters:**
- `floorId` (optional) - Filter by floor ID

### GET /rooms/:id
**Description:** Get room by ID  
**Authentication:** Required  
**Parameters:** `id` - Room ID

### PATCH /rooms/:id
**Description:** Update room (Admin only)  
**Authentication:** Required (Admin)  
**Parameters:** `id` - Room ID  
**Request Body:** UpdateRoomDto

### DELETE /rooms/:id
**Description:** Delete room (Admin only)
**Authentication:** Required (Admin)
**Parameters:** `id` - Room ID

## Zone Management Endpoints

### POST /zones
**Description:** Create a new zone (Admin only)
**Authentication:** Required (Admin)
**Request Body:** CreateZoneDto

### GET /zones
**Description:** Get all zones with optional filtering
**Authentication:** Required
**Query Parameters:**
- `roomId` (optional) - Filter by room ID

### GET /zones/:id
**Description:** Get zone by ID
**Authentication:** Required
**Parameters:** `id` - Zone ID

### PATCH /zones/:id
**Description:** Update zone (Admin only)
**Authentication:** Required (Admin)
**Parameters:** `id` - Zone ID
**Request Body:** UpdateZoneDto

### PATCH /zones/:id/disinfect
**Description:** Mark zone as disinfected
**Authentication:** Required
**Parameters:** `id` - Zone ID

### DELETE /zones/:id
**Description:** Delete zone (Admin only)
**Authentication:** Required (Admin)
**Parameters:** `id` - Zone ID

## History and Reporting Endpoints

### POST /history
**Description:** Log a new history record
**Authentication:** Required
**Request Body:** CreateHistoryDto

### GET /history
**Description:** Get all history records
**Authentication:** Required

### GET /history/reports
**Description:** Get history reports
**Authentication:** Required

### GET /history/export
**Description:** Export history data
**Authentication:** Required

### GET /history/:id
**Description:** Get history record by ID
**Authentication:** Required
**Parameters:** `id` - History record ID

### DELETE /history/:id
**Description:** Delete history record (Admin only)
**Authentication:** Required (Admin)
**Parameters:** `id` - History record ID

## Error Responses

All endpoints return standardized error responses:

```json
{
  "statusCode": 400,
  "timestamp": "2025-07-09T08:55:11.039Z",
  "path": "/api/v1/endpoint",
  "method": "POST",
  "error": "BadRequestException",
  "message": "Validation failed"
}
```

### Common HTTP Status Codes
- **200** - Success
- **201** - Created
- **400** - Bad Request (validation errors)
- **401** - Unauthorized (missing or invalid token)
- **403** - Forbidden (insufficient permissions)
- **404** - Not Found
- **500** - Internal Server Error

## Database Configuration

The API uses Firebase Firestore as the database. To connect with real Firebase credentials:

1. Create a Firebase project at https://console.firebase.google.com
2. Generate a service account key
3. Update the `.env` file with your Firebase credentials:

```env
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour-Private-Key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_CLIENT_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project.iam.gserviceaccount.com
FIREBASE_DATABASE_URL=https://your-project-default-rtdb.firebaseio.com/
```

## Development Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Build the server:**
   ```bash
   npm run build:server
   ```

3. **Start the development server:**
   ```bash
   npm run dev:server
   ```

4. **Start the production server:**
   ```bash
   npm run start:server
   ```

## Testing

The API includes comprehensive validation, authentication, and error handling:

- ✅ All endpoints are properly secured with Firebase authentication
- ✅ Role-based access control (Admin/User roles)
- ✅ Request validation using class-validator
- ✅ Comprehensive error handling and logging
- ✅ Health check endpoints for monitoring
- ✅ Swagger documentation for API exploration

## Architecture

- **Framework:** NestJS with TypeScript
- **Database:** Firebase Firestore
- **Authentication:** Firebase Auth with Bearer tokens
- **Validation:** class-validator with custom pipes
- **Documentation:** Swagger/OpenAPI
- **Logging:** Custom logging interceptor
- **Error Handling:** Global exception filter

## Notes

- The API is currently configured to run in development mode without requiring real Firebase credentials
- All endpoints are properly documented with Swagger annotations
- The server includes CORS support for frontend integration
- Request/response logging is enabled for debugging
- The API follows RESTful conventions and best practices
