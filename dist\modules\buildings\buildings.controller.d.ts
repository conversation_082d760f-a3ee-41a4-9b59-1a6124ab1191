import { BuildingsService } from "./buildings.service";
import type { CreateBuildingDto } from "./dto/create-building.dto";
import type { UpdateBuildingDto } from "./dto/update-building.dto";
export declare class BuildingsController {
    private readonly buildingsService;
    constructor(buildingsService: BuildingsService);
    create(createBuildingDto: CreateBuildingDto): Promise<{
        buildingId: string;
        createdAt: Date;
        buildingName: string;
        address: string;
        totalFloors: number;
        managerId: string;
        description?: string;
    }>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findOne(id: string): Promise<{
        id: string;
    }>;
    update(id: string, updateBuildingDto: UpdateBuildingDto): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
