import { Injectable, UnauthorizedException, BadRequestException } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"
import { UsersService } from "../users/users.service"
import type { RegisterDto } from "./dto/register.dto"
import { UserRole } from "../../common/decorators/roles.decorator"

@Injectable()
export class AuthService {
  constructor(
    private firebaseService: FirebaseService,
    private usersService: UsersService,
  ) {}

  async register(registerDto: RegisterDto) {
    try {
      const auth = this.firebaseService.getAuth()
      let userRecord: any

      if (auth && process.env.NODE_ENV !== 'development') {
        // Create user in Firebase Auth (production mode)
        userRecord = await auth.createUser({
          email: registerDto.email,
          password: registerDto.password,
          displayName: registerDto.username,
        })
      } else {
        // Mock user creation for development mode
        userRecord = {
          uid: `dev_user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          email: registerDto.email,
          displayName: registerDto.username,
        }
      }

      // Ensure role is of type UserRole
      const role: UserRole = registerDto.role ?? UserRole.USER

      // Create user document in Firestore
      const userData = {
        userId: userRecord.uid,
        username: registerDto.username,
        email: registerDto.email,
        role,
        language: registerDto.language || "en",
        createdAt: new Date(),
        lastLogin: undefined as Date | undefined,
      }

      await this.usersService.create(userData)

      return {
        message: "User registered successfully",
        userId: userRecord.uid,
        ...(process.env.NODE_ENV === 'development' && {
          note: "Development mode: User created locally without Firebase Auth"
        })
      }
    } catch (error) {
      // Use BadRequestException for registration errors
      throw new BadRequestException(`Registration failed: ${error.message}`)
    }
  }

  async validateUser(uid: string) {
    try {
      const userRecord = await this.firebaseService.getAuth().getUser(uid)
      const userData = await this.usersService.findById(uid)

      return {
        ...userRecord,
        ...userData,
      }
    } catch (error) {
      throw new UnauthorizedException("User validation failed")
    }
  }

  async updateLastLogin(userId: string) {
    await this.usersService.updateLastLogin(userId)
  }
}
