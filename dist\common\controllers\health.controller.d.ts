import { FirebaseService } from '../../config/firebase/firebase.service';
export declare class HealthController {
    private readonly firebaseService;
    constructor(firebaseService: FirebaseService);
    getHealth(): Promise<{
        status: string;
        timestamp: string;
        uptime: number;
        environment: string;
        services: {
            firebase: string;
        };
    }>;
    getFirebaseHealth(): Promise<{
        service: string;
        status: string;
        timestamp: string;
    }>;
}
