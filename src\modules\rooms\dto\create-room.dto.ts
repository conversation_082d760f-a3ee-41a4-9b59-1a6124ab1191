import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsBoolean, IsDateString } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"

export class CreateRoomDto {
  @ApiProperty()
  @IsString()
  roomNumber: string

  @ApiProperty()
  @IsString()
  roomName: string

  @ApiProperty()
  @IsString()
  floorId: string

  @ApiProperty()
  @IsString()
  roomType: string

  @ApiProperty()
  @IsNumber()
  area: number

  @ApiProperty()
  @IsNumber()
  capacity: number

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  requiresDisinfection?: boolean

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  lastDisinfected?: string
}
