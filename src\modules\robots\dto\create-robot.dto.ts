import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON>al, IsIP, IsDateString } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"

export class CreateRobotDto {
  @ApiProperty()
  @IsString()
  robotName: string

  @ApiProperty()
  @IsString()
  serialNumber: string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  batteryLevel?: number

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  currentPosition?: string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsIP()
  ipAddress?: string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  firmwareVersion?: string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  maintenanceDate?: string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  currentTaskId?: string
}
