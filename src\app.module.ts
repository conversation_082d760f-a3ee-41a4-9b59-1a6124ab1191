import { Modu<PERSON> } from "@nestjs/common"
import { ConfigModule } from "@nestjs/config"
import { AuthModule } from "./modules/auth/auth.module"
import { UsersModule } from "./modules/users/users.module"
import { TasksModule } from "./modules/tasks/tasks.module"
import { RobotsModule } from "./modules/robots/robots.module"
import { BuildingsModule } from "./modules/buildings/buildings.module"
import { FloorsModule } from "./modules/floors/floors.module"
import { RoomsModule } from "./modules/rooms/rooms.module"
import { ZonesModule } from "./modules/zones/zones.module"
import { HistoryModule } from "./modules/history/history.module"
import { FirebaseModule } from "./config/firebase/firebase.module"
import { HealthController } from "./common/controllers/health.controller"

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ".env",
      validationOptions: {
        allowUnknown: true,
        abortEarly: false,
      },
    }),
    FirebaseModule,
    AuthModule,
    UsersModule,
    TasksModule,
    RobotsModule,
    BuildingsModule,
    FloorsModule,
    RoomsModule,
    ZonesModule,
    HistoryModule,
  ],
  controllers: [HealthController],
})
export class AppModule {}
