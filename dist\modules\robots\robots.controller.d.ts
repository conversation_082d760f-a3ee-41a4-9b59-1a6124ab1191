import { RobotsService } from "./robots.service";
import type { CreateRobotDto } from "./dto/create-robot.dto";
import type { UpdateRobotDto } from "./dto/update-robot.dto";
import type { UpdateRobotStatusDto } from "./dto/update-robot-status.dto";
export declare class RobotsController {
    private readonly robotsService;
    constructor(robotsService: RobotsService);
    create(createRobotDto: CreateRobotDto): Promise<{
        robotId: string;
        isConnected: boolean;
        createdAt: Date;
        robotName: string;
        serialNumber: string;
        batteryLevel?: number;
        currentPosition?: string;
        ipAddress?: string;
        firmwareVersion?: string;
        maintenanceDate?: string;
        currentTaskId?: string;
    }>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findOne(id: string): Promise<{
        id: string;
    }>;
    update(id: string, updateRobotDto: UpdateRobotDto): Promise<{
        id: string;
    }>;
    updateStatus(id: string, status: UpdateRobotStatusDto): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
