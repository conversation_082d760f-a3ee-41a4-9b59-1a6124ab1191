import { Controller, Get, Post, Patch, Param, Delete, UseGuards, Query, Body } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQuery } from "@nestjs/swagger"
import { FloorsService } from "./floors.service"
import type { CreateFloorDto } from "./dto/create-floor.dto"
import type { UpdateFloorDto } from "./dto/update-floor.dto"
import { FirebaseAuthGuard } from "../../common/guards/firebase-auth.guard"
import { RolesGuard } from "../../common/guards/roles.guard"
import { Roles, UserRole } from "../../common/decorators/roles.decorator"

@ApiTags("Floors")
@Controller("floors")
@UseGuards(FirebaseAuthGuard, RolesGuard)
@ApiBearerAuth()
export class FloorsController {
  constructor(private readonly floorsService: FloorsService) {}

  @Post()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Create a new floor (Admin only)" })
  create(@Body() createFloorDto: CreateFloorDto) {
    return this.floorsService.create(createFloorDto)
  }

  @Get()
  @ApiOperation({ summary: 'Get all floors' })
  @ApiQuery({ name: 'buildingId', required: false })
  findAll(@Query('buildingId') buildingId?: string) {
    if (buildingId) {
      return this.floorsService.findByBuilding(buildingId);
    }
    return this.floorsService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get floor by ID' })
  findOne(@Param('id') id: string) {
    return this.floorsService.findById(id);
  }

  @Patch(":id")
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Update floor (Admin only)" })
  update(@Param('id') id: string, @Body() updateFloorDto: UpdateFloorDto) {
    return this.floorsService.update(id, updateFloorDto)
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete floor (Admin only)' })
  remove(@Param('id') id: string) {
    return this.floorsService.remove(id);
  }
}
