export declare enum TaskType {
    MANUAL_DISINFECTION = "manual_disinfection",
    SCHEDULED_DISINFECTION = "scheduled_disinfection",
    NAVIGATION = "navigation",
    MAINTENANCE = "maintenance"
}
export declare enum TaskPriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    URGENT = "urgent"
}
export declare class CreateTaskDto {
    taskName: string;
    taskType: TaskType;
    priority: TaskPriority;
    scheduledTime?: string;
    createdBy: string;
    robotId: string;
    zoneId: string;
    estimatedDuration?: number;
}
