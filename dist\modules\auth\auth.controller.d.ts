import { AuthService } from "./auth.service";
import type { RegisterDto } from "./dto/register.dto";
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    register(registerDto: RegisterDto): Promise<{
        message: string;
        userId: string;
    }>;
    getProfile(req: any): Promise<{
        id: string;
        uid: string;
        email?: string;
        emailVerified: boolean;
        displayName?: string;
        photoURL?: string;
        phoneNumber?: string;
        disabled: boolean;
        metadata: import("firebase-admin/auth").UserMetadata;
        providerData: import("firebase-admin/auth").UserInfo[];
        passwordHash?: string;
        passwordSalt?: string;
        customClaims?: {
            [key: string]: any;
        };
        tenantId?: string | null;
        tokensValidAfterTime?: string;
        multiFactor?: import("firebase-admin/auth").MultiFactorSettings;
    }>;
}
