import { Injectable, type OnModuleInit, Logger } from "@nestjs/common"
import { ConfigService } from "@nestjs/config"
import * as admin from "firebase-admin"
import { getFirestore } from "firebase-admin/firestore"
import { getAuth } from "firebase-admin/auth"

@Injectable()
export class FirebaseService implements OnModuleInit {
  private readonly logger = new Logger(FirebaseService.name)
  private firestore: admin.firestore.Firestore
  private auth: admin.auth.Auth

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    try {
      await this.initializeFirebase()
      this.logger.log('Firebase initialized successfully')
    } catch (error) {
      this.logger.error('Failed to initialize Firebase', error)
      this.logger.warn('Running in development mode without Firebase connection')
      // Don't throw error in development to allow server to start
      if (process.env.NODE_ENV === 'production') {
        throw error
      }
    }
  }

  private async initializeFirebase() {
    // Validate required environment variables
    const requiredVars = [
      'FIREBASE_PROJECT_ID',
      'FIREBASE_PRIVATE_KEY_ID',
      'FIREBASE_PRIVATE_KEY',
      'FIREBASE_CLIENT_EMAIL',
      'FIREBASE_CLIENT_ID'
    ]

    for (const varName of requiredVars) {
      const value = this.configService.get<string>(varName)
      if (!value) {
        throw new Error(`Missing required environment variable: ${varName}`)
      }
    }

    const serviceAccount = {
      type: "service_account",
      project_id: this.configService.get<string>("FIREBASE_PROJECT_ID"),
      private_key_id: this.configService.get<string>("FIREBASE_PRIVATE_KEY_ID"),
      private_key: this.configService.get<string>("FIREBASE_PRIVATE_KEY")?.replace(/\\n/g, "\n"),
      client_email: this.configService.get<string>("FIREBASE_CLIENT_EMAIL"),
      client_id: this.configService.get<string>("FIREBASE_CLIENT_ID"),
      auth_uri: "https://accounts.google.com/o/oauth2/auth",
      token_uri: "https://oauth2.googleapis.com/token",
      auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
      client_x509_cert_url: this.configService.get<string>("FIREBASE_CLIENT_CERT_URL"),
    }

    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
        databaseURL: this.configService.get<string>("FIREBASE_DATABASE_URL"),
      })
    }

    this.firestore = getFirestore()
    this.auth = getAuth()

    // Test the connection
    await this.testConnection()
  }

  private async testConnection() {
    try {
      // Test Firestore connection
      await this.firestore.collection('_health_check').limit(1).get()
      this.logger.log('Firestore connection test successful')
    } catch (error) {
      this.logger.warn('Firestore connection test failed', error)
      // Don't throw here as the collection might not exist yet
    }
  }

  getFirestore(): admin.firestore.Firestore {
    if (!this.firestore) {
      if (process.env.NODE_ENV === 'development') {
        this.logger.warn('Firestore not initialized - running in development mode')
        // Return a mock firestore for development
        return null as any
      }
      throw new Error('Firestore not initialized. Make sure Firebase service is properly configured.')
    }
    return this.firestore
  }

  getAuth(): admin.auth.Auth {
    if (!this.auth) {
      if (process.env.NODE_ENV === 'development') {
        this.logger.warn('Firebase Auth not initialized - running in development mode')
        // Return a mock auth for development
        return null as any
      }
      throw new Error('Firebase Auth not initialized. Make sure Firebase service is properly configured.')
    }
    return this.auth
  }

  async isHealthy(): Promise<boolean> {
    try {
      if (!this.firestore || !this.auth) {
        return false
      }

      // Simple health check
      await this.firestore.collection('_health_check').limit(1).get()
      return true
    } catch (error) {
      this.logger.error('Firebase health check failed', error)
      return false
    }
  }
}
