"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const app_module_1 = require("./app.module");
const http_exception_filter_1 = require("./common/filters/http-exception.filter");
const logging_interceptor_1 = require("./common/interceptors/logging.interceptor");
async function bootstrap() {
    const logger = new common_1.Logger('Bootstrap');
    try {
        const app = await core_1.NestFactory.create(app_module_1.AppModule, {
            logger: ['error', 'warn', 'log', 'debug', 'verbose'],
        });
        app.useGlobalFilters(new http_exception_filter_1.AllExceptionsFilter());
        app.useGlobalInterceptors(new logging_interceptor_1.LoggingInterceptor());
        app.useGlobalPipes(new common_1.ValidationPipe({
            whitelist: true,
            forbidNonWhitelisted: true,
            transform: true,
            disableErrorMessages: false,
        }));
        app.enableCors({
            origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
            credentials: true,
        });
        app.setGlobalPrefix("api/v1");
        const config = new swagger_1.DocumentBuilder()
            .setTitle("Robot Disinfection Management API")
            .setDescription("Backend API for robot disinfection management system")
            .setVersion("1.0")
            .addBearerAuth()
            .addTag('Health', 'Health check endpoints')
            .addTag('Auth', 'Authentication endpoints')
            .addTag('Users', 'User management endpoints')
            .addTag('Rooms', 'Room management endpoints')
            .addTag('Robots', 'Robot management endpoints')
            .addTag('Tasks', 'Task management endpoints')
            .addTag('History', 'History and reporting endpoints')
            .build();
        const document = swagger_1.SwaggerModule.createDocument(app, config);
        swagger_1.SwaggerModule.setup("api/docs", app, document);
        const port = process.env.PORT || 3001;
        await app.listen(port);
        logger.log(`🚀 Application is running on: http://localhost:${port}`);
        logger.log(`📚 Swagger docs available at: http://localhost:${port}/api/docs`);
        logger.log(`🏥 Health check available at: http://localhost:${port}/api/v1/health`);
    }
    catch (error) {
        logger.error('❌ Error starting the application', error);
        process.exit(1);
    }
}
bootstrap();
//# sourceMappingURL=main.js.map