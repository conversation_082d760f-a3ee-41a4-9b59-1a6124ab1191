import { FirebaseService } from "../../config/firebase/firebase.service";
import type { CreateUserDto } from "./dto/create-user.dto";
import type { UpdateUserDto } from "./dto/update-user.dto";
export declare class UsersService {
    private firebaseService;
    private readonly collection;
    constructor(firebaseService: FirebaseService);
    create(createUserDto: CreateUserDto): Promise<CreateUserDto>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findById(id: string): Promise<{
        id: string;
    }>;
    update(id: string, updateUserDto: UpdateUserDto): Promise<{
        id: string;
    }>;
    updateLastLogin(userId: string): Promise<void>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
