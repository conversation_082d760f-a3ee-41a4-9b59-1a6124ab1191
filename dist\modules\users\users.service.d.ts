import { FirebaseService } from "../../config/firebase/firebase.service";
import type { CreateUserDto } from "./dto/create-user.dto";
import type { UpdateUserDto } from "./dto/update-user.dto";
export declare class UsersService {
    private firebaseService;
    private readonly collection;
    private devUsers;
    constructor(firebaseService: FirebaseService);
    create(createUserDto: CreateUserDto): Promise<CreateUserDto>;
    findAll(): Promise<any[]>;
    findById(id: string): Promise<any>;
    update(id: string, updateUserDto: UpdateUserDto): Promise<any>;
    updateLastLogin(userId: string): Promise<void>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
